<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.IntentActivity"
        tools:targetApi="31">
        <!-- Sesuaikan dengan target API jika perlu -->

        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".Barang"
            android:exported="false"
            android:label="Barang"
            android:parentActivityName=".MainActivity">
            <!-- Tambahkan meta-data untuk mendukung parentActivityName pada API level yang lebih rendah jika diperlukan -->
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MainActivity" />
        </activity>

        <!-- Deklarasi activity lainnya bisa ditambahkan di sini -->

    </application>

</manifest>
